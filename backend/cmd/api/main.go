package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"goreal-backend/internal/config"
	"goreal-backend/internal/container"
	"goreal-backend/internal/handlers"
	"goreal-backend/internal/middleware"
	"goreal-backend/pkg/audit"
	"goreal-backend/pkg/health"
	"goreal-backend/pkg/jwt"
	"goreal-backend/pkg/observability"
	"goreal-backend/pkg/performance"
	redisClient "goreal-backend/pkg/redis"

	"github.com/redis/go-redis/v9"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize observability
	shutdown, err := observability.Init(cfg.ServiceName, cfg.Environment)
	if err != nil {
		log.Fatalf("Failed to initialize observability: %v", err)
	}
	defer shutdown()

	// Initialize Redis client for security features
	var redisConn *redisClient.Client
	if cfg.RateLimit.RedisEnabled {
		redisConn, err = redisClient.NewClient(cfg.Redis)
		if err != nil {
			log.Printf("Warning: Failed to initialize Redis client: %v", err)
			log.Println("Continuing without Redis-based features...")
		} else {
			defer redisConn.Close()
			log.Println("Redis client initialized successfully")
		}
	}

	// Initialize audit logger
	auditLogger := audit.NewConsoleLogger(cfg.Security.EnableAuditLog)

	// Initialize performance monitoring
	_, err = performance.NewQueryMetrics()
	if err != nil {
		log.Printf("Warning: Failed to initialize query metrics: %v", err)
	}

	httpMetrics, err := performance.NewHTTPMetrics()
	if err != nil {
		log.Printf("Warning: Failed to initialize HTTP metrics: %v", err)
	}

	memoryMonitor, err := performance.NewMemoryMonitor()
	if err != nil {
		log.Printf("Warning: Failed to initialize memory monitor: %v", err)
	}

	_, err = performance.NewCPUMonitor()
	if err != nil {
		log.Printf("Warning: Failed to initialize CPU monitor: %v", err)
	}

	// Initialize caching
	cacheConfig := performance.DefaultCacheConfig()
	inMemoryCache := performance.NewInMemoryCache(cacheConfig)

	// Initialize GC optimizer
	gcOptimizer := performance.NewGCOptimizer(memoryMonitor)

	// Initialize JWT manager with Redis support
	var redisClient *redis.Client
	if redisConn != nil {
		redisClient = redisConn.Client
	}
	_ = jwt.NewJWTManager(
		cfg.JWT.AccessSecret,
		cfg.JWT.RefreshSecret,
		cfg.JWT.AccessTokenExpiry,
		cfg.JWT.RefreshTokenExpiry,
		redisClient, // Pass the underlying Redis client
	)

	// Initialize services
	serviceContainer, err := container.NewContainer()
	if err != nil {
		log.Fatalf("Failed to initialize services: %v", err)
	}

	// Initialize health check manager
	healthManager := health.NewManager("goreal-backend", "1.0.0")

	// Add health checkers
	if serviceContainer.SupabaseClient != nil {
		// Note: We'll need to adapt this for Supabase's SQL interface
		// healthManager.AddChecker(health.NewDatabaseChecker(serviceContainer.SupabaseClient.DB, "supabase"))
	}

	if redisConn != nil {
		healthManager.AddChecker(health.NewRedisChecker(redisConn.Client, "redis"))
	}

	// Initialize handlers
	handlerContainer := handlers.NewContainer(
		serviceContainer.AuthService,
		serviceContainer.UserService,
		serviceContainer.ClientService,
		serviceContainer.LeadService,
		serviceContainer.SalesService,
		serviceContainer.TaskService,
		nil, // notificationService - not available in container yet
		serviceContainer.AnalyticsService,
	)

	// Setup router using Go 1.22+ standard library ServeMux
	mux := http.NewServeMux()

	// Create middleware chain
	var handler http.Handler = mux

	// Apply middleware in reverse order (last applied = first executed)
	handler = middleware.CORS(cfg.CORS)(handler)

	// Performance middleware (applied early for comprehensive tracking)
	if httpMetrics != nil {
		handler = httpMetrics.MetricsMiddleware()(handler)
	}

	// Compression middleware
	compressionConfig := performance.DefaultCompressionConfig()
	handler = performance.CompressionMiddleware(compressionConfig)(handler)

	// Cache control middleware
	cacheControlConfig := performance.DefaultCacheControlConfig()
	handler = performance.CacheControlMiddleware(cacheControlConfig)(handler)

	// Enhanced security middleware
	if cfg.Security.IPFilter.Enabled {
		handler = middleware.IPFilter(cfg.Security.IPFilter)(handler)
	}
	handler = middleware.RequestSizeLimit(cfg.Security.MaxRequestSize)(handler)
	handler = middleware.SecurityHeaders(cfg.Security)(handler)

	// Rate limiting with Redis if available
	if redisConn != nil {
		handler = middleware.SlidingWindowRateLimiter(redisConn.Client, cfg.RateLimit)(handler)
	} else {
		handler = middleware.RateLimiter(cfg.RateLimit)(handler)
	}

	// Audit logging middleware
	handler = audit.Middleware(auditLogger)(handler)

	// Observability and error handling
	handler = middleware.Observability(handler)
	handler = middleware.ErrorHandler(handler)
	handler = middleware.RequestLogger(handler)

	// Setup API routes using standard library patterns
	setupAPIRoutes(mux, handlerContainer, serviceContainer)

	// Start background performance optimization
	if gcOptimizer != nil {
		go func() {
			ticker := time.NewTicker(30 * time.Second)
			defer ticker.Stop()
			for range ticker.C {
				gcOptimizer.OptimizeGC()
			}
		}()
	}

	// Add health check endpoint
	mux.HandleFunc("GET /health", healthManager.Handler())

	// Add readiness check endpoint
	mux.HandleFunc("GET /ready", func(w http.ResponseWriter, r *http.Request) {
		// Simple readiness check - can be enhanced based on requirements
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "ready",
			"service": "goreal-backend",
		})
	})

	// Add performance monitoring endpoints
	if httpMetrics != nil || memoryMonitor != nil {
		mux.HandleFunc("GET /metrics/performance", func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")

			stats := make(map[string]interface{})
			if memoryMonitor != nil {
				stats["memory"] = memoryMonitor.GetMemoryStats()
			}
			if inMemoryCache != nil {
				stats["cache"] = inMemoryCache.GetStats()
			}
			if gcOptimizer != nil {
				stats["gc"] = gcOptimizer.GetGCStats()
			}

			json.NewEncoder(w).Encode(stats)
		})
	}

	// Create server with optimized settings
	srv := &http.Server{
		Addr:         fmt.Sprintf(":%s", cfg.Port),
		Handler:      handler,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
		// Optimize connection handling
		MaxHeaderBytes: 1 << 20, // 1MB
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting server on port %s", cfg.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

// setupAPIRoutes configures all API routes using Go 1.22+ ServeMux patterns
func setupAPIRoutes(mux *http.ServeMux, handlerContainer *handlers.Container, serviceContainer *container.Container) {
	// Authentication routes (public) - no authentication required
	setupAuthRoutes(mux, handlerContainer.AuthHandler)

	// Protected routes - require authentication
	setupProtectedRoutes(mux, handlerContainer, serviceContainer)
}

// setupAuthRoutes configures authentication endpoints
func setupAuthRoutes(mux *http.ServeMux, authHandler *handlers.AuthHandlerStdlib) {
	// Apply JSON content type middleware to auth routes
	jsonMiddleware := middleware.JSONContentType

	mux.Handle("POST /api/auth/login", jsonMiddleware(http.HandlerFunc(authHandler.Login)))
	mux.Handle("POST /api/auth/register", jsonMiddleware(http.HandlerFunc(authHandler.Register)))
	mux.Handle("POST /api/auth/refresh", jsonMiddleware(http.HandlerFunc(authHandler.RefreshToken)))
	mux.Handle("POST /api/auth/logout", jsonMiddleware(http.HandlerFunc(authHandler.Logout)))
	mux.Handle("POST /api/auth/reset-password", jsonMiddleware(http.HandlerFunc(authHandler.ResetPassword)))
	mux.Handle("POST /api/auth/confirm-reset", jsonMiddleware(http.HandlerFunc(authHandler.ConfirmPasswordReset)))
}

// setupProtectedRoutes configures routes that require authentication
func setupProtectedRoutes(mux *http.ServeMux, handlerContainer *handlers.Container, serviceContainer *container.Container) {
	// Create middleware chain for protected routes
	authMiddleware := middleware.AuthRequired(serviceContainer.AuthService)
	jsonMiddleware := middleware.JSONContentType

	// Combine middlewares: JSON + Auth
	protectedMiddleware := func(h http.Handler) http.Handler {
		return jsonMiddleware(authMiddleware(h))
	}

	// User management routes
	setupUserRoutes(mux, handlerContainer.UserHandler, protectedMiddleware)

	// Task management routes
	setupTaskRoutes(mux, handlerContainer.TaskHandler, protectedMiddleware)

	// Notification routes
	setupNotificationRoutes(mux, handlerContainer.NotificationHandler, protectedMiddleware)

	// Analytics routes
	setupAnalyticsRoutes(mux, handlerContainer.AnalyticsHandler, protectedMiddleware)

	// Client management routes (require employee+ role)
	setupClientRoutes(mux, handlerContainer.ClientHandler, protectedMiddleware, serviceContainer)

	// Admin-only routes
	setupAdminRoutes(mux, protectedMiddleware, serviceContainer)

	// TODO: Add other protected routes when handlers are implemented
	// - Lead management routes
	// - Sales management routes
}

// setupUserRoutes configures user management endpoints
func setupUserRoutes(mux *http.ServeMux, userHandler *handlers.UserHandlerStdlib, middleware func(http.Handler) http.Handler) {
	if userHandler == nil {
		return // Handler not implemented yet
	}

	// User profile routes
	mux.Handle("GET /api/users/profile", middleware(http.HandlerFunc(userHandler.GetProfile)))
	mux.Handle("PUT /api/users/profile", middleware(http.HandlerFunc(userHandler.UpdateProfile)))
	mux.Handle("POST /api/users/avatar", middleware(http.HandlerFunc(userHandler.UploadAvatar)))

	// User management routes
	mux.Handle("GET /api/users/{id}", middleware(http.HandlerFunc(userHandler.GetUser)))
	mux.Handle("GET /api/users/{id}/stats", middleware(http.HandlerFunc(userHandler.GetUserStats)))
	mux.Handle("POST /api/users/{id}/follow", middleware(http.HandlerFunc(userHandler.FollowUser)))
	mux.Handle("DELETE /api/users/{id}/follow", middleware(http.HandlerFunc(userHandler.UnfollowUser)))
}

// setupTaskRoutes configures task management endpoints
func setupTaskRoutes(mux *http.ServeMux, taskHandler *handlers.TaskHandlerStdlib, middleware func(http.Handler) http.Handler) {
	if taskHandler == nil {
		return // Handler not implemented yet
	}

	// Basic CRUD operations
	mux.Handle("GET /api/tasks", middleware(http.HandlerFunc(taskHandler.ListTasks)))
	mux.Handle("POST /api/tasks", middleware(http.HandlerFunc(taskHandler.CreateTask)))
	mux.Handle("GET /api/tasks/{id}", middleware(http.HandlerFunc(taskHandler.GetTask)))
	mux.Handle("PUT /api/tasks/{id}", middleware(http.HandlerFunc(taskHandler.UpdateTask)))
	mux.Handle("DELETE /api/tasks/{id}", middleware(http.HandlerFunc(taskHandler.DeleteTask)))

	// Task operations
	mux.Handle("POST /api/tasks/{id}/complete", middleware(http.HandlerFunc(taskHandler.CompleteTask)))
	mux.Handle("GET /api/tasks/assigned/{userID}", middleware(http.HandlerFunc(taskHandler.GetTasksByUser)))
	mux.Handle("GET /api/tasks/overdue", middleware(http.HandlerFunc(taskHandler.GetOverdueTasks)))
	mux.Handle("POST /api/tasks/bulk-assign", middleware(http.HandlerFunc(taskHandler.BulkAssignTasks)))
}

// setupNotificationRoutes configures notification management endpoints
func setupNotificationRoutes(mux *http.ServeMux, notificationHandler *handlers.NotificationHandlerStdlib, middleware func(http.Handler) http.Handler) {
	if notificationHandler == nil {
		return // Handler not implemented yet
	}

	// User notification routes
	mux.Handle("GET /api/notifications", middleware(http.HandlerFunc(notificationHandler.GetUserNotifications)))
	mux.Handle("PUT /api/notifications/{id}/read", middleware(http.HandlerFunc(notificationHandler.MarkAsRead)))
	mux.Handle("PUT /api/notifications/read-all", middleware(http.HandlerFunc(notificationHandler.MarkAllAsRead)))
	mux.Handle("GET /api/notifications/unread-count", middleware(http.HandlerFunc(notificationHandler.GetUnreadCount)))

	// Admin notification routes (these would need admin middleware)
	mux.Handle("POST /api/notifications", middleware(http.HandlerFunc(notificationHandler.CreateNotification)))
	mux.Handle("POST /api/notifications/bulk", middleware(http.HandlerFunc(notificationHandler.SendBulkNotification)))
	mux.Handle("POST /api/notifications/email", middleware(http.HandlerFunc(notificationHandler.SendEmailNotification)))
	mux.Handle("POST /api/notifications/push", middleware(http.HandlerFunc(notificationHandler.SendPushNotification)))
}

// setupAnalyticsRoutes configures analytics endpoints
func setupAnalyticsRoutes(mux *http.ServeMux, analyticsHandler *handlers.AnalyticsHandlerStdlib, middleware func(http.Handler) http.Handler) {
	if analyticsHandler == nil {
		return // Handler not implemented yet
	}

	// Dashboard and general analytics
	mux.Handle("GET /api/analytics/dashboard", middleware(http.HandlerFunc(analyticsHandler.GetDashboardStats)))

	// Specific analytics endpoints
	mux.Handle("GET /api/analytics/sales", middleware(http.HandlerFunc(analyticsHandler.GetSalesAnalytics)))
	mux.Handle("GET /api/analytics/leads", middleware(http.HandlerFunc(analyticsHandler.GetLeadAnalytics)))
	mux.Handle("GET /api/analytics/properties", middleware(http.HandlerFunc(analyticsHandler.GetPropertyAnalytics)))
	mux.Handle("GET /api/analytics/financial", middleware(http.HandlerFunc(analyticsHandler.GetFinancialAnalytics)))

	// User performance analytics
	mux.Handle("GET /api/analytics/users/{userID}/performance", middleware(http.HandlerFunc(analyticsHandler.GetUserPerformance)))

	// Report generation
	mux.Handle("POST /api/analytics/reports/generate", middleware(http.HandlerFunc(analyticsHandler.GenerateReport)))
}

// setupClientRoutes configures client management endpoints (employee+ role required)
func setupClientRoutes(mux *http.ServeMux, clientHandler *handlers.ClientHandler, baseMiddleware func(http.Handler) http.Handler, serviceContainer *container.Container) {
	if clientHandler == nil {
		return // Handler not implemented yet - will be updated when ClientHandlerStdlib is created
	}

	// Create middleware chain: Base + Employee role requirement
	employeeMiddleware := func(h http.Handler) http.Handler {
		return baseMiddleware(middleware.EmployeeOrAbove()(h))
	}

	// TODO: Update these routes when ClientHandlerStdlib is implemented
	// Client CRUD operations would go here
	_ = employeeMiddleware // Prevent unused variable error
}

// setupAdminRoutes configures admin-only endpoints
func setupAdminRoutes(mux *http.ServeMux, baseMiddleware func(http.Handler) http.Handler, serviceContainer *container.Container) {
	// Create middleware chain: Base + Admin role requirement
	adminMiddleware := func(h http.Handler) http.Handler {
		return baseMiddleware(middleware.AdminOnly()(h))
	}

	// TODO: Add admin-only routes when implemented
	// - System configuration endpoints
	// - User role management endpoints
	// - System analytics endpoints
	_ = adminMiddleware // Prevent unused variable error
}
