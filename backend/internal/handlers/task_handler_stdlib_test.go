package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"goreal-backend/internal/domain"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockTaskService is a mock implementation of domain.TaskService
type MockTaskService struct {
	mock.Mock
}

func (m *MockTaskService) Create(ctx context.Context, req *domain.CreateTaskRequest) (*domain.Task, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*domain.Task), args.Error(1)
}

func (m *MockTaskService) GetByID(ctx context.Context, id uuid.UUID) (*domain.Task, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*domain.Task), args.Error(1)
}

func (m *MockTaskService) Update(ctx context.Context, id uuid.UUID, req *domain.UpdateTaskRequest) (*domain.Task, error) {
	args := m.Called(ctx, id, req)
	return args.Get(0).(*domain.Task), args.Error(1)
}

func (m *MockTaskService) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTaskService) GetByUser(ctx context.Context, userID uuid.UUID, filters domain.TaskFilters) ([]*domain.Task, error) {
	args := m.Called(ctx, userID, filters)
	return args.Get(0).([]*domain.Task), args.Error(1)
}

func (m *MockTaskService) GetByAssignee(ctx context.Context, assigneeID uuid.UUID, filters domain.TaskFilters) ([]*domain.Task, error) {
	args := m.Called(ctx, assigneeID, filters)
	return args.Get(0).([]*domain.Task), args.Error(1)
}

func (m *MockTaskService) MarkComplete(ctx context.Context, id uuid.UUID, userID uuid.UUID) (*domain.Task, error) {
	args := m.Called(ctx, id, userID)
	return args.Get(0).(*domain.Task), args.Error(1)
}

func (m *MockTaskService) AssignTask(ctx context.Context, id uuid.UUID, assigneeID uuid.UUID, assignedBy uuid.UUID) (*domain.Task, error) {
	args := m.Called(ctx, id, assigneeID, assignedBy)
	return args.Get(0).(*domain.Task), args.Error(1)
}

func (m *MockTaskService) BulkAssign(ctx context.Context, taskIDs []uuid.UUID, userID uuid.UUID) error {
	args := m.Called(ctx, taskIDs, userID)
	return args.Error(0)
}

func (m *MockTaskService) List(ctx context.Context, filters domain.TaskFilters) ([]*domain.Task, error) {
	args := m.Called(ctx, filters)
	return args.Get(0).([]*domain.Task), args.Error(1)
}

func (m *MockTaskService) GetByAssignedUser(ctx context.Context, userID uuid.UUID) ([]*domain.Task, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*domain.Task), args.Error(1)
}

func (m *MockTaskService) CompleteTask(ctx context.Context, taskID uuid.UUID, notes string) error {
	args := m.Called(ctx, taskID, notes)
	return args.Error(0)
}

func (m *MockTaskService) GetOverdueTasks(ctx context.Context) ([]*domain.Task, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*domain.Task), args.Error(1)
}

func (m *MockTaskService) GetTasksByEntity(ctx context.Context, entityType string, entityID uuid.UUID) ([]*domain.Task, error) {
	args := m.Called(ctx, entityType, entityID)
	return args.Get(0).([]*domain.Task), args.Error(1)
}

func TestTaskHandlerStdlib_CreateTask(t *testing.T) {
	userID := uuid.New()
	user := &domain.User{
		ID:    userID,
		Email: "<EMAIL>",
		Role:  domain.RoleEmployee,
	}

	tests := []struct {
		name           string
		requestBody    *domain.CreateTaskRequest
		setupContext   func(*http.Request) *http.Request
		mockSetup      func(*MockTaskService)
		expectedStatus int
		expectedError  string
	}{
		{
			name: "successful task creation",
			requestBody: &domain.CreateTaskRequest{
				Title:       "Test Task",
				Description: stringPtr("Test Description"),
				Priority:    stringPtr("high"),
			},
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup: func(m *MockTaskService) {
				task := &domain.Task{
					ID:          uuid.New(),
					Title:       "Test Task",
					Description: stringPtr("Test Description"),
					Priority:    domain.TaskPriorityHigh,
					CreatedBy:   userID,
				}
				m.On("Create", mock.Anything, mock.AnythingOfType("*domain.CreateTaskRequest")).Return(task, nil)
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name: "missing user context",
			requestBody: &domain.CreateTaskRequest{
				Title: "Test Task",
			},
			setupContext: func(req *http.Request) *http.Request {
				return req // No user in context
			},
			mockSetup:      func(m *MockTaskService) {},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Authentication required",
		},
		{
			name: "missing title",
			requestBody: &domain.CreateTaskRequest{
				Description: stringPtr("Test Description"),
			},
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup: func(m *MockTaskService) {
				m.On("Create", mock.Anything, mock.AnythingOfType("*domain.CreateTaskRequest")).Return((*domain.Task)(nil), domain.ErrRequiredField)
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Failed to create task",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockTaskService := new(MockTaskService)
			tt.mockSetup(mockTaskService)
			
			handler := NewTaskHandlerStdlib(mockTaskService)

			// Create request
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/api/tasks", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			req = tt.setupContext(req)
			w := httptest.NewRecorder()

			// Execute
			handler.CreateTask(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response["error"], tt.expectedError)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Task created successfully", response["message"])
				assert.NotNil(t, response["data"])
			}

			mockTaskService.AssertExpectations(t)
		})
	}
}

func TestTaskHandlerStdlib_GetTask(t *testing.T) {
	taskID := uuid.New()
	userID := uuid.New()
	user := &domain.User{
		ID:    userID,
		Email: "<EMAIL>",
		Role:  domain.RoleEmployee,
	}

	tests := []struct {
		name           string
		taskID         string
		setupContext   func(*http.Request) *http.Request
		mockSetup      func(*MockTaskService)
		expectedStatus int
		expectedError  string
	}{
		{
			name:   "successful task retrieval",
			taskID: taskID.String(),
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup: func(m *MockTaskService) {
				task := &domain.Task{
					ID:        taskID,
					Title:     "Test Task",
					CreatedBy: userID,
				}
				m.On("GetByID", mock.Anything, taskID).Return(task, nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:   "invalid task ID",
			taskID: "invalid-uuid",
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup:      func(m *MockTaskService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid task ID",
		},
		{
			name:   "task not found",
			taskID: taskID.String(),
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup: func(m *MockTaskService) {
				m.On("GetByID", mock.Anything, taskID).Return((*domain.Task)(nil), domain.ErrNotFound)
			},
			expectedStatus: http.StatusNotFound,
			expectedError:  "Task not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockTaskService := new(MockTaskService)
			tt.mockSetup(mockTaskService)
			
			handler := NewTaskHandlerStdlib(mockTaskService)

			// Create request with path value
			req := httptest.NewRequest("GET", "/api/tasks/"+tt.taskID, nil)
			req = tt.setupContext(req)
			
			// Simulate Go 1.22+ path value
			req.SetPathValue("id", tt.taskID)
			
			w := httptest.NewRecorder()

			// Execute
			handler.GetTask(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response["error"], tt.expectedError)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.NotNil(t, response["data"])
			}

			mockTaskService.AssertExpectations(t)
		})
	}
}

func TestTaskHandlerStdlib_UpdateTask(t *testing.T) {
	taskID := uuid.New()
	userID := uuid.New()
	user := &domain.User{
		ID:    userID,
		Email: "<EMAIL>",
		Role:  domain.RoleEmployee,
	}

	tests := []struct {
		name           string
		taskID         string
		requestBody    *domain.UpdateTaskRequest
		setupContext   func(*http.Request) *http.Request
		mockSetup      func(*MockTaskService)
		expectedStatus int
		expectedError  string
	}{
		{
			name:   "successful task update",
			taskID: taskID.String(),
			requestBody: &domain.UpdateTaskRequest{
				Title:    stringPtr("Updated Task"),
				Priority: stringPtr("medium"),
			},
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup: func(m *MockTaskService) {
				updatedTask := &domain.Task{
					ID:        taskID,
					Title:     "Updated Task",
					Priority:  domain.TaskPriorityMedium,
					CreatedBy: userID,
				}
				m.On("Update", mock.Anything, taskID, mock.AnythingOfType("*domain.UpdateTaskRequest")).Return(updatedTask, nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:   "invalid task ID",
			taskID: "invalid-uuid",
			requestBody: &domain.UpdateTaskRequest{
				Title: stringPtr("Updated Task"),
			},
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup:      func(m *MockTaskService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid task ID",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockTaskService := new(MockTaskService)
			tt.mockSetup(mockTaskService)
			
			handler := NewTaskHandlerStdlib(mockTaskService)

			// Create request
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("PUT", "/api/tasks/"+tt.taskID, bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			req = tt.setupContext(req)
			
			// Simulate Go 1.22+ path value
			req.SetPathValue("id", tt.taskID)
			
			w := httptest.NewRecorder()

			// Execute
			handler.UpdateTask(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response["error"], tt.expectedError)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Task updated successfully", response["message"])
				assert.NotNil(t, response["data"])
			}

			mockTaskService.AssertExpectations(t)
		})
	}
}

func TestTaskHandlerStdlib_MarkComplete(t *testing.T) {
	taskID := uuid.New()
	userID := uuid.New()
	user := &domain.User{
		ID:    userID,
		Email: "<EMAIL>",
		Role:  domain.RoleEmployee,
	}

	tests := []struct {
		name           string
		taskID         string
		setupContext   func(*http.Request) *http.Request
		mockSetup      func(*MockTaskService)
		expectedStatus int
		expectedError  string
	}{
		{
			name:   "successful task completion",
			taskID: taskID.String(),
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup: func(m *MockTaskService) {
				m.On("CompleteTask", mock.Anything, taskID, "").Return(nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:   "service error",
			taskID: taskID.String(),
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup: func(m *MockTaskService) {
				m.On("CompleteTask", mock.Anything, taskID, "").Return(domain.ErrNotFound)
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Failed to complete task",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockTaskService := new(MockTaskService)
			tt.mockSetup(mockTaskService)
			
			handler := NewTaskHandlerStdlib(mockTaskService)

			// Create request
			req := httptest.NewRequest("POST", "/api/tasks/"+tt.taskID+"/complete", nil)
			req = tt.setupContext(req)
			
			// Simulate Go 1.22+ path value
			req.SetPathValue("id", tt.taskID)
			
			w := httptest.NewRecorder()

			// Execute
			handler.CompleteTask(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response["error"], tt.expectedError)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Task completed successfully", response["message"])
			}

			mockTaskService.AssertExpectations(t)
		})
	}
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}
