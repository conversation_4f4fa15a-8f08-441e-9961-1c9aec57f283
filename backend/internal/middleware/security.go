// ============================================================================
// ENHANCED SECURITY MIDDLEWARE
// ============================================================================

package middleware

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"goreal-backend/internal/config"

	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

var securityTracer = otel.Tracer("goreal-backend/security")

// ============================================================================
// SECURITY HEADERS MIDDLEWARE
// ============================================================================

// SecurityHeaders adds comprehensive security headers
func SecurityHeaders(cfg config.SecurityConfig) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx, span := securityTracer.Start(r.Context(), "middleware.SecurityHeaders")
			defer span.End()

			// Content Security Policy
			csp := buildCSP(cfg.CSP)
			if csp != "" {
				w.Header().Set("Content-Security-Policy", csp)
			}

			// Security headers
			w.Header().Set("X-Content-Type-Options", "nosniff")
			w.Header().Set("X-Frame-Options", "DENY")
			w.Header().Set("X-XSS-Protection", "1; mode=block")
			w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
			w.Header().Set("Permissions-Policy", "camera=(), microphone=(), geolocation=(), payment=()")

			// HSTS (only for HTTPS)
			if r.TLS != nil || r.Header.Get("X-Forwarded-Proto") == "https" {
				w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload")
			}

			// Remove server information
			w.Header().Set("Server", "")

			// Add request ID for tracking
			requestID := generateRequestID()
			w.Header().Set("X-Request-ID", requestID)
			r = r.WithContext(context.WithValue(ctx, "request_id", requestID))

			span.SetAttributes(
				attribute.String("request.id", requestID),
				attribute.String("security.headers", "applied"),
			)

			next.ServeHTTP(w, r)
		})
	}
}

// ============================================================================
// REDIS RATE LIMITING MIDDLEWARE
// ============================================================================

// RedisRateLimiter provides distributed rate limiting using Redis
func RedisRateLimiter(redisClient *redis.Client, cfg config.RateLimitConfig) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx, span := securityTracer.Start(r.Context(), "middleware.RedisRateLimiter")
			defer span.End()

			// Get client identifier
			clientID := getClientIdentifier(r)

			// Create rate limit key
			window := time.Now().Unix() / int64(cfg.WindowSeconds)
			key := fmt.Sprintf("rate_limit:%s:%d", clientID, window)

			span.SetAttributes(
				attribute.String("rate_limit.client_id", clientID),
				attribute.String("rate_limit.key", key),
				attribute.Int("rate_limit.limit", cfg.RequestsPerWindow),
			)

			// Check current count
			pipe := redisClient.Pipeline()
			incrCmd := pipe.Incr(ctx, key)
			_ = pipe.Expire(ctx, key, time.Duration(cfg.WindowSeconds)*time.Second)

			_, err := pipe.Exec(ctx)
			if err != nil {
				span.RecordError(err)
				// If Redis is down, allow the request but log the error
				span.SetAttributes(attribute.String("rate_limit.status", "redis_error"))
				next.ServeHTTP(w, r)
				return
			}

			count := incrCmd.Val()

			// Set rate limit headers
			w.Header().Set("X-RateLimit-Limit", strconv.Itoa(cfg.RequestsPerWindow))
			w.Header().Set("X-RateLimit-Remaining", strconv.Itoa(max(0, cfg.RequestsPerWindow-int(count))))
			w.Header().Set("X-RateLimit-Reset", strconv.FormatInt((window+1)*int64(cfg.WindowSeconds), 10))

			if count > int64(cfg.RequestsPerWindow) {
				span.SetAttributes(
					attribute.String("rate_limit.status", "exceeded"),
					attribute.Int64("rate_limit.count", count),
				)

				w.Header().Set("Retry-After", strconv.Itoa(cfg.WindowSeconds))
				http.Error(w, `{"error":"Rate limit exceeded","code":"RATE_LIMIT_EXCEEDED"}`, http.StatusTooManyRequests)
				return
			}

			span.SetAttributes(
				attribute.String("rate_limit.status", "allowed"),
				attribute.Int64("rate_limit.count", count),
			)

			next.ServeHTTP(w, r)
		})
	}
}

// ============================================================================
// ADVANCED RATE LIMITING WITH SLIDING WINDOW
// ============================================================================

// SlidingWindowRateLimiter implements sliding window rate limiting
func SlidingWindowRateLimiter(redisClient *redis.Client, cfg config.RateLimitConfig) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx, span := securityTracer.Start(r.Context(), "middleware.SlidingWindowRateLimiter")
			defer span.End()

			clientID := getClientIdentifier(r)
			now := time.Now()
			windowStart := now.Add(-time.Duration(cfg.WindowSeconds) * time.Second)

			key := fmt.Sprintf("sliding_rate_limit:%s", clientID)

			// Lua script for atomic sliding window rate limiting
			luaScript := `
				local key = KEYS[1]
				local window_start = tonumber(ARGV[1])
				local now = tonumber(ARGV[2])
				local limit = tonumber(ARGV[3])
				local ttl = tonumber(ARGV[4])
				
				-- Remove old entries
				redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
				
				-- Count current entries
				local current = redis.call('ZCARD', key)
				
				if current < limit then
					-- Add current request
					redis.call('ZADD', key, now, now)
					redis.call('EXPIRE', key, ttl)
					return {1, current + 1}
				else
					return {0, current}
				end
			`

			result, err := redisClient.Eval(ctx, luaScript, []string{key},
				windowStart.UnixNano(), now.UnixNano(), cfg.RequestsPerWindow, cfg.WindowSeconds*2).Result()

			if err != nil {
				span.RecordError(err)
				// If Redis is down, allow the request
				next.ServeHTTP(w, r)
				return
			}

			resultSlice := result.([]interface{})
			allowed := resultSlice[0].(int64) == 1
			count := resultSlice[1].(int64)

			// Set rate limit headers
			w.Header().Set("X-RateLimit-Limit", strconv.Itoa(cfg.RequestsPerWindow))
			w.Header().Set("X-RateLimit-Remaining", strconv.Itoa(max(0, cfg.RequestsPerWindow-int(count))))
			w.Header().Set("X-RateLimit-Reset", strconv.FormatInt(now.Add(time.Duration(cfg.WindowSeconds)*time.Second).Unix(), 10))

			if !allowed {
				span.SetAttributes(
					attribute.String("rate_limit.status", "exceeded"),
					attribute.Int64("rate_limit.count", count),
				)

				w.Header().Set("Retry-After", strconv.Itoa(cfg.WindowSeconds))
				http.Error(w, `{"error":"Rate limit exceeded","code":"RATE_LIMIT_EXCEEDED"}`, http.StatusTooManyRequests)
				return
			}

			span.SetAttributes(
				attribute.String("rate_limit.status", "allowed"),
				attribute.Int64("rate_limit.count", count),
			)

			next.ServeHTTP(w, r)
		})
	}
}

// ============================================================================
// IP WHITELIST/BLACKLIST MIDDLEWARE
// ============================================================================

// IPFilter provides IP-based access control
func IPFilter(cfg config.IPFilterConfig) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			_, span := securityTracer.Start(r.Context(), "middleware.IPFilter")
			defer span.End()

			clientIP := getClientIP(r)

			span.SetAttributes(attribute.String("client.ip", clientIP))

			// Check blacklist first
			if isIPInList(clientIP, cfg.Blacklist) {
				span.SetAttributes(attribute.String("ip_filter.status", "blacklisted"))
				http.Error(w, `{"error":"Access denied","code":"IP_BLACKLISTED"}`, http.StatusForbidden)
				return
			}

			// If whitelist is configured, check it
			if len(cfg.Whitelist) > 0 && !isIPInList(clientIP, cfg.Whitelist) {
				span.SetAttributes(attribute.String("ip_filter.status", "not_whitelisted"))
				http.Error(w, `{"error":"Access denied","code":"IP_NOT_WHITELISTED"}`, http.StatusForbidden)
				return
			}

			span.SetAttributes(attribute.String("ip_filter.status", "allowed"))
			next.ServeHTTP(w, r)
		})
	}
}

// ============================================================================
// REQUEST SIZE LIMITING MIDDLEWARE
// ============================================================================

// RequestSizeLimit limits the size of request bodies
func RequestSizeLimit(maxSize int64) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			_, span := securityTracer.Start(r.Context(), "middleware.RequestSizeLimit")
			defer span.End()

			if r.ContentLength > maxSize {
				span.SetAttributes(
					attribute.Int64("request.content_length", r.ContentLength),
					attribute.Int64("request.max_size", maxSize),
					attribute.String("request_size.status", "exceeded"),
				)

				http.Error(w, `{"error":"Request too large","code":"REQUEST_TOO_LARGE"}`, http.StatusRequestEntityTooLarge)
				return
			}

			// Limit the request body reader
			r.Body = http.MaxBytesReader(w, r.Body, maxSize)

			span.SetAttributes(
				attribute.Int64("request.content_length", r.ContentLength),
				attribute.String("request_size.status", "allowed"),
			)

			next.ServeHTTP(w, r)
		})
	}
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

// getClientIdentifier returns a unique identifier for rate limiting
func getClientIdentifier(r *http.Request) string {
	// Try to get user ID from context first (for authenticated requests)
	if userID := r.Context().Value("user_id"); userID != nil {
		return fmt.Sprintf("user:%s", userID)
	}

	// Fall back to IP address
	return fmt.Sprintf("ip:%s", getClientIP(r))
}

// isIPInList checks if an IP is in the given list (supports CIDR)
func isIPInList(ip string, list []string) bool {
	for _, item := range list {
		if item == ip {
			return true
		}
		// TODO: Add CIDR support here
	}
	return false
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// buildCSP builds Content Security Policy header
func buildCSP(csp config.CSPConfig) string {
	if !csp.Enabled {
		return ""
	}

	var policies []string

	if csp.DefaultSrc != "" {
		policies = append(policies, fmt.Sprintf("default-src %s", csp.DefaultSrc))
	}
	if csp.ScriptSrc != "" {
		policies = append(policies, fmt.Sprintf("script-src %s", csp.ScriptSrc))
	}
	if csp.StyleSrc != "" {
		policies = append(policies, fmt.Sprintf("style-src %s", csp.StyleSrc))
	}
	if csp.ImgSrc != "" {
		policies = append(policies, fmt.Sprintf("img-src %s", csp.ImgSrc))
	}
	if csp.ConnectSrc != "" {
		policies = append(policies, fmt.Sprintf("connect-src %s", csp.ConnectSrc))
	}
	if csp.FontSrc != "" {
		policies = append(policies, fmt.Sprintf("font-src %s", csp.FontSrc))
	}
	if csp.ObjectSrc != "" {
		policies = append(policies, fmt.Sprintf("object-src %s", csp.ObjectSrc))
	}
	if csp.MediaSrc != "" {
		policies = append(policies, fmt.Sprintf("media-src %s", csp.MediaSrc))
	}
	if csp.FrameSrc != "" {
		policies = append(policies, fmt.Sprintf("frame-src %s", csp.FrameSrc))
	}

	return strings.Join(policies, "; ")
}

// max returns the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
